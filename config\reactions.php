<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Reaction
    |--------------------------------------------------------------------------
    |
    | This option defines the default reaction type that will be used when
    | users click the main reaction button without selecting a specific reaction.
    |
    */
    'default' => 'like',

    /*
    |--------------------------------------------------------------------------
    | Reaction Types
    |--------------------------------------------------------------------------
    |
    | Here you can define all available reaction types with their emojis,
    | labels, and colors. The key will be used as the reaction type identifier.
    |
    */
    'types' => [
        'like' => [
            'emoji' => '👍',
            'label' => 'Like',
            'color' => 'text-blue-600',
        ],
        'love' => [
            'emoji' => '❤️',
            'label' => 'Love',
            'color' => 'text-red-600',
        ],
        'haha' => [
            'emoji' => '😂',
            'label' => 'Haha',
            'color' => 'text-yellow-600',
        ],
        'wow' => [
            'emoji' => '😮',
            'label' => 'Wow',
            'color' => 'text-purple-600',
        ],
        'sad' => [
            'emoji' => '😢',
            'label' => 'Sad',
            'color' => 'text-blue-500',
        ],
        'angry' => [
            'emoji' => '😡',
            'label' => 'Angry',
            'color' => 'text-red-500',
        ],
    ],
];
